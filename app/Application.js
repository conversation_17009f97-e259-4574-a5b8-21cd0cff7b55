/**
 * The main application class. An instance of this class is created by app.js when it
 * calls Ext.application(). This is the ideal place to handle application launch and
 * initialization details.
 */
Ext.define('MyAppName.Application', {
    extend: 'Ext.app.Application',

    name: 'MyAppName',

    quickTips: false,
    platformConfig: {
        desktop: {
            quickTips: true
        }
    },

    // onAppUpdate: function (abc) {
    //     var employee = { name: '<PERSON>', age: 28 };
    //     //const { name, age } = employee;  //  If run the first two lines then it is working 
    //     var { name, age, company = 'Eastland' } = abc || employee; //~ If run the first and last line then it is throwing an error

    //     let object = { myProperty: "hello"};
	// 	let property = object?.myProperty.big();

    //     console.log(property);

    //     const testObj = { one: 1, two: 2 };
    //     Object.entries(testObj).forEach(([key, value]) => {
    //         console.log(`key: ${key}, value: ${value}`);
    //     });

    //     this.devendra();
    //     this.foo();

    //     Ext.Msg.confirm('Application Update', 'This application has an update, reload?',
    //         function (choice) {
    //             if (choice === 'yes' && name == 'Gary') {
    //                 window.location.reload();
    //             }
    //         }
    //     );
    // },
    // devendra: function (adventurer = {}) {
    //     // Each of these fail
    //     var a = this?.foo();
    //     const foo = null ?? 'default string';
    //     const fn = ([x, y, z]) => ({ x, y, z });
    //     const dogName = adventurer.dog?.name;

    //     console.log({foo, fn, dogName});
    // },
    // // This is ok, apparently
    // foo: function (...bar) {
    //     return 1;
    // },
    // // Utility method using spread and rest parameters
    // mergeConfigs: function(baseConfig, ...configs) {
    //     // Merge multiple config objects
    //     return configs.reduce((merged, config) => ({
    //         ...merged,
    //         ...config
    //     }), { ...baseConfig });
    // },

    // sdk2176: function() {
    //     const employee = { name: 'Gary', age: 28 };
    //     //const { name, age } = employee;  //  If run the first two lines then it is working 
    //     const { name, age, company = 'Eastland' } = employee; // If run the first and last line then it is throwing an error

    //     console.log({ name, age, company });
    // }

    // namedRegexFn: function() {
    //     const URL_PATTERN = /^(?<protocol>https?):\/\/(?<domain>[a-zA-Z0-9.-]+)(?::(?<port>\d+))?(?<path>\/[^\s?]*)?(?:\?(?<query>[^\s#]*))?(?:#(?<fragment>[^\s]*))?$/;

    //     const DATE_PATTERN = /^(?<year>\d{4})-(?<month>\d{2})-(?<day>\d{2})$/;

    //     const match = url.match(URL_PATTERN);
    //     if (!match || !match.groups) {
    //         return null;
    //     }
        
    //     return {
    //         protocol: match.groups['protocol'],
    //         domain: match.groups['domain'],
    //         port: match.groups['port'] || null,
    //         path: match.groups['path'] || null,
    //         query: match.groups['query'] || null,
    //         fragment: match.groups['fragment'] || null
    //     };
    // }
});
