Ext.define("MyAppName.Application",{extend:"Ext.app.Application",name:"MyAppN<PERSON>",quickTips:!1,platformConfig:{desktop:{quickTips:!0}},onAppUpdate:function($abc_property$$){var $employee$$={name:"<PERSON>",age:28},{name:$name$$}=$abc_property$$||$employee$$;let $$jscomp$optchain$tmp1816076405$0$$;$abc_property$$=null==($$jscomp$optchain$tmp1816076405$0$$={myProperty:"hello"})?void 0:$$jscomp$optchain$tmp1816076405$0$$.myProperty.big();console.log($abc_property$$);Object.entries({one:1,two:2}).forEach(([$key$$,
$value$$])=>{console.log(`key: ${$key$$}, value: ${$value$$}`)});this.devendra();this.foo();Ext.Msg.confirm("Application Update","This application has an update, reload?",function($choice$$){"yes"===$choice$$&&"Gary"==$name$$&&window.location.reload()})},devendra:function($adventurer_dogName$$={}){null==this||this.foo();let $$jscomp$optchain$tmp1816076405$2$$;$adventurer_dogName$$=null==($$jscomp$optchain$tmp1816076405$2$$=$adventurer_dogName$$.dog)?void 0:$$jscomp$optchain$tmp1816076405$2$$.name;
console.log({foo:"default string",fn:([$x$$,$y$$,$z$$])=>({x:$x$$,y:$y$$,z:$z$$}),dogName:$adventurer_dogName$$})},foo:function(...$bar$$){return 1},mergeConfigs:function($baseConfig$$,...$configs$$){return $configs$$.reduce(($merged$$,$config$$)=>({...$merged$$,...$config$$}),{...$baseConfig$$})},namedRegexFn:function(){const $match$$=url.match(/^(?<protocol>https?):\/\/(?<domain>[a-zA-Z0-9.-]+)(?::(?<port>\d+))?(?<path>\/[^\s?]*)?(?:\?(?<query>[^\s#]*))?(?:#(?<fragment>[^\s]*))?$/);return $match$$&&
$match$$.groups?{protocol:$match$$.groups.protocol,domain:$match$$.groups.domain,port:$match$$.groups.port||null,path:$match$$.groups.path||null,query:$match$$.groups.query||null,fragment:$match$$.groups.fragment||null}:null}});
