# This file is used by the root build scripts as well as subordinate builds. The
# properties contained in this file may be overridden by the build system at build
# time so check there (in Team City) for any overrides. These values are used by
# local builds as well but these can be overridden by the "local.properties" file
# so check there as well.

# NOTE - DO NOT STORE PASSWORDS, ACCESS TOKENS OR KEYS OF ANY KIND IN THIS FILE

version.major=7
version.minor=9
version.patch=0
version.build=12345
version.full=${version.major}.${version.minor}.${version.patch}.${version.build}

# Older aliases for version.full
tools.version.number=${version.full}
tools.version=${version.full}

tools.version.label=beta

sencha.maven.repo.url=http://eye.sencha.com/artifactory

sencha.maven.repo.id=com.sencha.mavenrepo

teamcity.hostname=teamcity.sencha.com

teamcity.username=buildclient
#teamcity.password=passwords don't go here! use local.properties or pass on command-line

sencha.dir=${basedir}/sencha-command
sencha.build.dir=${sencha.dir}/.build
sencha.dist.dir=${sencha.dir}/dist/SenchaCmd

sencha.compass.dir=${basedir}/sencha-command-compass
sencha.compass.build.dir=${sencha.compass.dir}/build
sencha.compass.dist.dir=${sencha.compass.dir}/dist

sencha.fashion.dir=${basedir}/sencha-fashion
sencha.fashion.build.dir=${sencha.fashion.dir}/build
sencha.fashion.dist.dir=${sencha.fashion.dir}/dist

sencha.test.dir=${basedir}/sencha-command-test
sencha.test.build.dir=${sencha.test.dir}/build
sencha.test.dist.dir=${sencha.test.dir}/dist

sencha.service.dir=${basedir}/sencha-command-service
sencha.service.build.dir=${sencha.service.dir}/build
sencha.service.dist.dir=${sencha.service.dir}/dist

sencha.test.harness.dir=${basedir}/sencha-test-harness
sencha.test.harness.build.dir=${sencha.test.harness.dir}/build
sencha.test.harness.dist.dir=${sencha.test.harness.dir}/dist

dist.dir=${build.dir}/dist
temp.dir=${build.dir}/temp
staging.dir=${temp.dir}/staging

build.name=nightly
product.name=Sencha Cmd ${tools.version.number}

jira.host=sencha.jira.com
jira.browse.url=https://${jira.host}/browse
jira.search.base=/sr/jira.issueviews:searchrequest-xml/temp/SearchRequest.xml
jira.max.results=1000

deploy.qa.ssh.key=
deploy.qa.dest.host=<EMAIL>
deploy.qa.dest.path=/mnt/ebs/knightly/cmd

outputfile.osx=SenchaCmd-${tools.version}-osx.app

outputfile.windows=SenchaCmd-${tools.version}-windows.exe

outputfile.linux=SenchaCmd-${tools.version}-linux.run

outputfile.linux64=SenchaCmd-${tools.version}-linux-x64.run

# This is deliberately place here so passwords in build.xml don't work (easily)
teamcity.password=passwords don't go here! use local.properties or pass on command-line

teamcity.url.prefix=http://${teamcity.hostname}/httpAuth/repository/download
teamcity.url.all.prefix=http://${teamcity.hostname}/httpAuth/repository/downloadAll

teamcity.ext.41.build.num=2
teamcity.ext.42.build.num=23
teamcity.touch.21.build.num=12
teamcity.touch.22.build.num=65
teamcity.touch.23.build.num=205
teamcity.stbuild.build.num=6

teamcity.ext.41.current.version=4.1.3
teamcity.ext.42.current.version=4.2.2
teamcity.touch.21.current.version=2.1.1
teamcity.touch.22.current.version=2.2.1
teamcity.touch.23.current.version=2.3.1a

teamcity.ext.build.url=${teamcity.url.prefix}/bt${teamcity.ext.41.build.num}/.lastPinned
teamcity.ext42.build.url=${teamcity.url.prefix}/bt${teamcity.ext.42.build.num}/.lastPinned

teamcity.stbuild.build.url=${teamcity.url.all.prefix}/bt${teamcity.stbuild.build.num}/.lastPinned

teamcity.touch.build.url=${teamcity.url.prefix}/bt${teamcity.touch.21.build.num}/.lastPinned
teamcity.touch22.build.url=${teamcity.url.prefix}/bt${teamcity.touch.22.build.num}/.lastPinned
teamcity.touch23.build.url=${teamcity.url.prefix}/bt${teamcity.touch.23.build.num}/.lastPinned

teamcity.ext.config.file=${teamcity.ext42.build.url}/sencha.cfg

teamcity.touch.config.file=${teamcity.touch23.build.url}/sencha.cfg

build.cache.dir=${basedir}/cache
build.cache.sdk.dir=${build.cache.dir}/sdk
build.cache.win.dir=${build.cache.dir}/win32
build.cache.mac.dir=${build.cache.dir}/mac
build.cache.lin32.dir=${build.cache.dir}/lin32
build.cache.lin64.dir=${build.cache.dir}/lin64

deploy.sencha.test=true
# empty suffix, will be set by build server
surefire.reportNameSuffix=

#BitRock install builder settings
installbuilder.dir=/Applications/BitRock InstallBuilder for Qt 8.1.0/
installbuilder.sub.dir=/bin/Builder.app/Contents/MacOS/
installbuilder.bin=./installbuilder.sh
installbuilder.zip.dir=${installbuilder.dir}/output
