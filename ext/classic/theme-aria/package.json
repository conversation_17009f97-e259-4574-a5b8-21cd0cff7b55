{"name": "@sencha/ext-classic-theme-aria", "ext-react-name": "@sencha/ext-react-classic-theme-aria", "ext-name": "@sencha/ext-classic-theme-aria", "SenchaExtName": "@sencha/ext-classic-theme-aria", "SenchaExtReactName": "@sencha/ext-react-classic-theme-aria", "version": "********", "sencha": {"name": "theme-aria", "namespace": "Ext", "type": "theme", "extend": "theme-neptune", "alternateName": ["ext-theme-aria"], "framework": "ext", "toolkit": "classic", "creator": "<PERSON><PERSON>", "summary": "Ext JS Aria Theme", "detailedDescription": "A high-contrast accessibility theme based on Neptune", "version": "********", "compatVersion": "7.4.0", "format": "1", "output": "${workspace.subpkg.prefix}/${package.name}", "local": true, "sass": {"namespace": "Ext", "etc": "${package.dir}/sass/etc/all.scss", "var": "${package.dir}/sass/var", "src": "${package.dir}/sass/src"}, "classpath": "${package.dir}/src", "overrides": "${package.dir}/overrides", "slicer": {"js": []}, "properties": {"skip.pkg": 1}}}