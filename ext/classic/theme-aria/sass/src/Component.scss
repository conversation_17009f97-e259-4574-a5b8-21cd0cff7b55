//.#{$prefix}aria-highcontrast .#{$prefix}form-trigger:after {
//    content: theme-background-image("form/trigger");
//}
//
//.#{$prefix}aria-highcontrast .#{$prefix}form-date-trigger:after {
//    content: theme-background-image("form/date-trigger") !important;
//}
//
//.#{$prefix}aria-highcontrast .#{$prefix}aria-item-focus,
//.#{$prefix}aria-highcontrast .#{$prefix}datepicker-selected {
//    border: 2px dotted yellow !important;
//    text-decoration: underline;
//    font-weight: bold;
//}
//
//.#{$prefix}aria-highcontrast .#{$prefix}form-invalid-icon:after {
//    content: theme-background-image('form/exclamation');
//}
//
//.#{$prefix}aria-highcontrast .#{$prefix}tool-resize:after {
//    content: theme-background-image('tools/tool-resize') !important;
//}
//
//.#{$prefix}aria-highcontrast .#{$prefix}tool-move:after {
//    content: theme-background-image('tools/tool-move') !important;
//}
//
//.#{$prefix}aria-highcontrast .#{$prefix}tool-close:after {
//    content: theme-background-image('tools/tool-close') !important;
//}
//
//.#{$prefix}aria-highcontrast .#{$prefix}tool-collapse:after {
//    content: theme-background-image('tools/tool-collapse') !important;
//}
//
//.#{$prefix}aria-highcontrast .#{$prefix}tool-expand:after {
//    content: theme-background-image('tools/tool-expand') !important;
//}
//
//.#{$prefix}aria-highcontrast .#{$prefix}datepicker-next a:after {
//    content: theme-background-image('shared/right-btn');
//}
//
//.#{$prefix}aria-highcontrast .#{$prefix}datepicker-prev a:after {
//    content: theme-background-image('shared/left-btn');
//}
//
//.#{$prefix}aria-highcontrast table.#{$prefix}datepicker-inner .#{$prefix}datepicker-today a {
//    border: 4px outset;
//    font-weight: bold;
//}

