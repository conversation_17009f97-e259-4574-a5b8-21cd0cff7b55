{"name": "@sencha/ext-classic", "ext-react-name": "@sencha/ext-react-classic", "ext-name": "@sencha/ext-classic", "SenchaExtName": "@sencha/ext-classic", "SenchaExtReactName": "@sencha/ext-react-classic", "version": "********", "sencha": {"name": "classic", "namespace": "Ext", "type": "toolkit", "framework": "ext", "requires": ["core"], "creator": "<PERSON><PERSON>", "summary": "The Classic UI Toolkit", "detailedDescription": "UI components for desktops and legacy browsers", "version": "********", "compatVersion": "7.4.0", "format": "1", "output": "${framework.dir}/build", "local": true, "build": {"dir": "${package.output}"}, "sass": {"namespace": "Ext", "etc": "${package.dir}/sass/etc/all.scss", "var": "${package.dir}/sass/var", "src": "${package.dir}/sass/src"}, "classpath": "${package.dir}/src", "overrides": "${package.dir}/overrides", "buildOptions": {"classic": true}, "example": {"path": ["examples"]}, "subpkgs": {"dir": "${package.dir}/../", "packages": ["ux", "locale", "theme-classic", "theme-gray", "theme-classic-sandbox", "theme-neptune", "theme-neptune-touch", "theme-crisp", "theme-crisp-touch", "aria", "theme-aria", "theme-triton", "theme-graphite", "theme-material"]}, "language": {"js": {"input": {"version": "ES5"}}}, "properties": {"skip.sass": 1, "skip.pkg": 1, "skip.slice": 1}}}