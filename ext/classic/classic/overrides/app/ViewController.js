/**
 * @class Ext.app.ViewController
 */

/**
 * @method beforeRender
 * @template
 * Template method called by the owning component's 
 * {@link Ext.Component#method-beforeRender beforeRender} method.
 * @param {Ext.Component} component The owner component attached to the 
 * ViewController
 */

/**
 * @method afterRender
 * @template
 * Template method called by the owning component's 
 * {@link Ext.Component#method-afterRender afterRender} method.
 * @param {Ext.Component} component The owner component attached to the 
 * ViewController
 */

/**
 * @method boxReady
 * @template
 * Template method called by the owning component's 
 * {@link Ext.Component#method-onBoxReady onBoxReady} method.
 * @param {Ext.Component} component The owner component attached to the 
 * ViewController
 */
