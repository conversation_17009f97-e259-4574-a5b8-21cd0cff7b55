Ext.define('Ext.rtl.tip.QuickTipManager', {
    override: 'Ext.tip.QuickTipManager',

    init: function() {
        var me = this;

        // Will return false if not ready to proceed
        if (me.callParent(arguments) !== false) {
            me.tip.on('beforeshow', me.onBeforeFirstShow, me, { single: true });
        }
    },

    onBeforeFirstShow: function(tip) {
        // The rtl override for Component reads the DOM for floating components to
        // determine if their local coordinate system is RTL and caches the value.  If
        // QuickTipManager.init() is called before the Viewport has been rendered then the
        // cached value may be incorrect.  Clear the cached value so that the next call to
        // isLocalRtl() will read the DOM again. 
        tip._isOffsetParentRtl = undefined;
    }
});
