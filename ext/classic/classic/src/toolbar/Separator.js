/**
 * A simple class that adds a vertical separator bar between toolbar items
 * (css class: 'x-toolbar-separator').
 *
 *     @example
 *     Ext.create('Ext.panel.Panel', {
 *         title: 'Toolbar Separator Example',
 *         width: 300,
 *         height: 200,
 *         tbar : [
 *             'Item 1',
 *             { xtype: 'tbseparator' },
 *             'Item 2'
 *         ],
 *         renderTo: Ext.getBody()
 *     });
 */
Ext.define('Ext.toolbar.Separator', {
    extend: 'Ext.toolbar.Item',
    alias: 'widget.tbseparator',
    alternateClassName: 'Ext.Toolbar.Separator',

    // Toolbar required here because we'll try to decorate it's alternateClassName
    // with this class' alternate name
    requires: ['Ext.toolbar.Toolbar'],

    baseCls: Ext.baseCSSPrefix + 'toolbar-separator',

    ariaRole: 'separator'
});
