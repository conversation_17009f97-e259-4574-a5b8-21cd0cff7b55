// @define Ext.layout.SizePolicy

/**
 * This class describes how a layout will interact with a component it manages.
 * 
 * There are special instances of this class stored as static properties to avoid object
 * instantiation. All instances of this class should be treated as readonly objects.
 *
 * @class Ext.layout.SizePolicy
 * @protected
 */

/**
 * @property {Boolean} readsWidth
 * Indicates that the `width` of the component is consumed.
 * @readonly
 */

/**
 * @property {Boolean} readsHeight
 * Indicates that the `height` of the component is consumed.
 * @readonly
 */

/**
 * @property {Boolean} setsWidth
 * Indicates that the `width` of the component will be set (i.e., calculated).
 * @readonly
 */

/**
 * @property {Boolean} setsHeight
 * Indicates that the `height` of the component will be set (i.e., calculated).
 * @readonly
 */
