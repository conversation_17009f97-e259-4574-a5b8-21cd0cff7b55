/**
 * @class Ext.fx.target.CompositeElementCSS
 * 
 * This class represents a animation target for a {@link Ext.CompositeElement}, where the
 * constituent elements support CSS based animation. It allows each {@link Ext.dom.Element} in 
 * the group to be animated as a whole. In general this class will not be created directly, 
 * the {@link Ext.CompositeElement} will be passed to the animation and the appropriate target 
 * will be created.
 */
Ext.define('Ext.fx.target.CompositeElementCSS', {
    extend: 'Ext.fx.target.CompositeElement',

    requires: ['Ext.fx.target.ElementCSS'],

    setAttr: function() {
        return Ext.fx.target.ElementCSS.prototype.setAttr.apply(this, arguments);
    }
});
