/**
 * @class Ext.fx.target.Target

This class specifies a generic target for an animation. It provides a wrapper around a
series of different types of objects to allow for a generic animation API.
A target can be a single object or a Composite object containing other objects that are 
to be animated. This class and it's subclasses are generally not created directly, the 
underlying animation will create the appropriate Ext.fx.target.Target object by passing 
the instance to be animated.

The following types of objects can be animated:

- {@link Ext.fx.target.Component Components}
- {@link Ext.fx.target.Element Elements}
- {@link Ext.fx.target.Sprite Sprites}

 * @abstract
 */
Ext.define('Ext.fx.target.Target', {
    isAnimTarget: true,

    /**
     * Creates new Target.
     * @param {Ext.Component/Ext.dom.Element/Ext.draw.sprite.Sprite} target The object
     * to be animated
     */
    constructor: function(target) {
        this.target = target;
        this.id = this.getId();
    },

    getId: function() {
        return this.target.id;
    },

    remove: function() {
        Ext.destroy(this.target);
    }
});
