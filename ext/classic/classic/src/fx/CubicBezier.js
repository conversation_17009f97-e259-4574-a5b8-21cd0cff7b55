/**
 * @private
 */
Ext.define('Ext.fx.CubicBezier', {
    singleton: true,

    cubicBezierAtTime: function(t, p1x, p1y, p2x, p2y, duration) {
        var cx = 3 * p1x,
            bx = 3 * (p2x - p1x) - cx,
            ax = 1 - cx - bx,
            cy = 3 * p1y,
            by = 3 * (p2y - p1y) - cy,
            ay = 1 - cy - by;

        function sampleCurveX(t) {
            return ((ax * t + bx) * t + cx) * t;
        }

        function solve(x, epsilon) {
            var t = solveCurveX(x, epsilon);

            return ((ay * t + by) * t + cy) * t;
        }

        function solveCurveX(x, epsilon) {
            var t0, t1, t2, x2, d2, i;

            for (t2 = x, i = 0; i < 8; i++) {
                x2 = sampleCurveX(t2) - x;

                if (Math.abs(x2) < epsilon) {
                    return t2;
                }

                d2 = (3 * ax * t2 + 2 * bx) * t2 + cx;

                if (Math.abs(d2) < 1e-6) {
                    break;
                }

                t2 = t2 - x2 / d2;
            }

            t0 = 0;
            t1 = 1;
            t2 = x;

            if (t2 < t0) {
                return t0;
            }

            if (t2 > t1) {
                return t1;
            }

            while (t0 < t1) {
                x2 = sampleCurveX(t2);

                if (Math.abs(x2 - x) < epsilon) {
                    return t2;
                }

                if (x > x2) {
                    t0 = t2;
                }
                else {
                    t1 = t2;
                }

                t2 = (t1 - t0) / 2 + t0;
            }

            return t2;
        }

        return solve(t, 1 / (200 * duration));
    },

    cubicBezier: function(x1, y1, x2, y2) {
        var fn = function(pos) {
            return Ext.fx.CubicBezier.cubicBezierAtTime(pos, x1, y1, x2, y2, 1);
        };

        fn.toCSS3 = function() {
            return 'cubic-bezier(' + [x1, y1, x2, y2].join(',') + ')';
        };

        fn.reverse = function() {
            return Ext.fx.CubicBezier.cubicBezier(1 - x2, 1 - y2, 1 - x1, 1 - y1);
        };

        return fn;
    }
});
