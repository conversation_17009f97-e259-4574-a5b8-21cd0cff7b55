/**
 * @private
 * Private utility class for managing all {@link Ext.form.field.Radio} fields grouped by name.
 */
Ext.define('Ext.form.RadioManager', {
    extend: 'Ext.util.MixedCollection',
    singleton: true,

    getByName: function(name, formId) {
        return this.filterBy(function(item) {
            return item.name === name && item.getFormId() === formId;
        });
    },

    getWithValue: function(name, value, formId) {
        return this.filterBy(function(item) {
            return item.name === name &&
                   item.inputValue == value && // eslint-disable-line eqeqeq
                   item.getFormId() === formId;
        });
    },

    getChecked: function(name, formId) {
        return this.findBy(function(item) {
            return item.name === name && item.checked && item.getFormId() === formId;
        });
    }
});
