/**
 * Italian translation
 * 2016-06-28 updated by <PERSON><PERSON><PERSON> (update to ExtJs 6.0.2)
 * 2012-05-28 updated by <PERSON><PERSON><PERSON> (many changes, update to 4.1.0)
 * 2007-12-21 updated by <PERSON>
 * 2007-10-04 updated by eric_void
 */
Ext.onReady(function() {
    if (Ext.Date) {
        Ext.Date.monthNames = ["<PERSON>nai<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>iu<PERSON>", "<PERSON><PERSON><PERSON>", "Agos<PERSON>", "Set<PERSON>bre", "Ottobre", "<PERSON><PERSON>bre", "<PERSON>cembre"];

        Ext.Date.getShortMonthName = function(month) {
            return Ext.Date.monthNames[month].substring(0, 3);
        };

        Ext.Date.monthNumbers = {
            Gen: 0,
            Feb: 1,
            Mar: 2,
            Apr: 3,
            Mag: 4,
            Giu: 5,
            Lug: 6,
            Ago: 7,
            Set: 8,
            Ott: 9,
            Nov: 10,
            Dic: 11
        };

        Ext.Date.getMonthNumber = function(name) {
            return Ext.Date.monthNumbers[name.substring(0, 1).toUpperCase() + name.substring(1, 3).toLowerCase()];
        };

        Ext.Date.dayNames = ["Domenica", "Lunedi", "Martedi", "Mercoledi", "Giovedi", "Venerdi", "Sabato"];

        Ext.Date.getShortDayName = function(day) {
            return Ext.Date.dayNames[day].substring(0, 3);
        };
    }

    if (Ext.util && Ext.util.Format) {
        Ext.apply(Ext.util.Format, {
            thousandSeparator: '.',
            decimalSeparator: ',',
            currencySign: '\u20ac', // Euro
            dateFormat: 'd/m/Y'
        });
    }
});
