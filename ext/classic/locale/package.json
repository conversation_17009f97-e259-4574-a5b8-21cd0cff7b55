{"name": "@sencha/ext-classic-locale", "ext-react-name": "@sencha/ext-react-classic-locale", "ext-name": "@sencha/ext-classic-locale", "SenchaExtName": "@sencha/ext-classic-locale", "SenchaExtReactName": "@sencha/ext-react-classic-locale", "version": "********", "sencha": {"name": "locale", "namespace": "Ext", "type": "code", "alternateName": ["ext-locale"], "framework": "ext", "toolkit": "classic", "creator": "<PERSON><PERSON>", "summary": "Classic toolkit locale", "detailedDescription": "Localization for Classic toolkit UI components", "version": "********", "compatVersion": "7.4.0", "format": "1", "output": "${workspace.subpkg.prefix}/${package.name}", "local": true, "build": {"dir": "${package.output}"}, "sass": {"namespace": "Ext", "etc": "${package.dir}/sass/etc/all.scss", "var": "${package.dir}/sass/var", "src": "${package.dir}/sass/src"}, "classpath": "${package.dir}/src", "overrides": "${package.dir}/overrides/${package.locale}"}}